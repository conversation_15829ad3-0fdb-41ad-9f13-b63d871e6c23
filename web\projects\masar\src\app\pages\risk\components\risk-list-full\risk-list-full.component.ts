import {
    Component,
    Input,
    <PERSON><PERSON><PERSON><PERSON>,
    On<PERSON><PERSON>roy,
    OnInit,
    SimpleChanges,
    ViewChild,
} from '@angular/core';
import { Filter, TableController, TableResult } from '@masar/common/misc/table';
import {
    EvaluationScoreBand,
    Risk,
    RiskImpact,
    RiskProbability,
} from '@masar/common/models';
import { forkJoin, Observable, Subject } from 'rxjs';
import { RiskListComponent } from '@masar/pages/risk/components/risk-list/risk-list.component';
import { generateRiskLevels } from '@masar/pages/risk/utils/generate-risk-levels.util';
import { AppSettingFetcherService, MiscApiService } from '@masar/core/services';
import { RiskService } from '@masar/pages/risk/risk.service';
import { ActivatedRoute, Router } from '@angular/router';
import { EvaluateService } from '@masar/features/evaluate/services/evaluate.service';
import { TranslateService } from '@ngx-translate/core';
import { ShownFilter } from '@masar/pages/risk/components/risk-list-full/shown-filter.type';
import { tableListFilters } from './table-list-filters.data';
import { TableListFilter } from '@masar/features/dynamic-page/types';
import { takeUntil } from 'rxjs/operators';

interface FilterData {
    keyword: string;
    categoryIds: string[];
    managementStrategyIds: string[];
    impactIds: string[];
    probabilityIds: string[];
    levels: number[];
    departmentIds: string[];
    strategicGoalIds: string[];
    evaluationStatus: -1 | 0 | 1;
    evaluationScoreBandIds: string[];
    types: string[];
}

@Component({
    selector: 'app-risk-list-full',
    templateUrl: './risk-list-full.component.html',
})
export class RiskListFullComponent implements OnInit, OnDestroy, OnChanges {
    @ViewChild('list') public list: RiskListComponent;

    @Input() public isFilterInUrl = false;

    @Input() public shownFilters: ShownFilter[] = [
        'name',
        'category',
        'management_strategy',
        'impact',
        'probability',
        'level',
        'department',
        'strategic_goal',
        'risk_type',
    ];

    @Input() public alternateListCallback: (
        filter: Filter<FilterData>
    ) => Observable<TableResult<Risk>>;

    public tableController: TableController<Risk, FilterData>;

    public tableListFilters?: TableListFilter[];

    public evaluationScoreBands: EvaluationScoreBand[] = [];

    public evaluationId: string = null;
    public showRiskType: boolean;
    private unsubscribeAll = new Subject();

    public constructor(
        private miscApiService: MiscApiService,
        private riskService: RiskService,
        private activatedRoute: ActivatedRoute,
        private router: Router,
        private evaluateService: EvaluateService,
        private translateService: TranslateService,
        private appSettingFetcherService: AppSettingFetcherService
    ) {}

    public ngOnInit(): void {
        this.initTableController();
        this.loadItems();
    }

    public ngOnDestroy(): void {
        this.tableController.stop();
    }

    public ngOnChanges(changes: SimpleChanges): void {
        const alternateListCallbackChange = changes['alternateListCallback'];
        if (!alternateListCallbackChange) return;

        this.reload();
    }

    public refreshItems(resetPageNumber: boolean = false): void {
        this.tableController.filter$.next(resetPageNumber);
    }

    public loadEvaluationScoreBands(evaluationId: string): void {
        this.evaluateService
            .listScoreBands(evaluationId, 'risk')
            .subscribe(items => {
                this.evaluationScoreBands = items;
            });
    }

    private reload(): void {
        this.initTableController();
    }

    private initTableController(): void {
        this.tableController?.stop();
        this.tableController = null;

        const callback: (
            filter: Filter<FilterData>
        ) => Observable<TableResult<Risk>> =
            this.alternateListCallback ??
            (filter => {
                return this.riskService.list2(
                    filter.data.keyword,
                    filter.data.categoryIds,
                    filter.data.managementStrategyIds,
                    filter.data.impactIds,
                    filter.data.probabilityIds,
                    filter.data.levels,
                    filter.data.departmentIds,
                    filter.data.strategicGoalIds,
                    filter.data.evaluationStatus,
                    filter.data.evaluationScoreBandIds,
                    this.evaluationId,
                    filter.data.types,
                    filter.pageNumber,
                    filter.pageSize
                );
            });

        this.tableController = new TableController<Risk, FilterData>(
            callback,
            {
                data: {
                    keyword: '',
                    categoryIds: [],
                    managementStrategyIds: [],
                    impactIds: [],
                    probabilityIds: [],
                    levels: [],
                    departmentIds: [],
                    strategicGoalIds: [],
                    evaluationStatus: 0,
                    evaluationScoreBandIds: [],
                    types: [],
                },
                pageSize: 20,
            },
            this.isFilterInUrl
                ? {
                      routingControls: {
                          router: this.router,
                          activatedRoute: this.activatedRoute,
                      },
                  }
                : {}
        );
        this.tableController.start();
    }

    private loadItems(): void {
        this.appSettingFetcherService.get$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(data => {
                this.showRiskType = data.riskSetting.optionalFields.find(
                    x => x.name === 'type'
                ).isEnabled;
            });
        forkJoin({
            riskImpact: this.miscApiService.getList('risk-impact'),
            riskProbability: this.miscApiService.getList('risk-probability'),
        }).subscribe(({ riskImpact, riskProbability }) => {
            const levels = generateRiskLevels(
                riskImpact as RiskImpact[],
                riskProbability as RiskProbability[]
            )
                .sort((a, b) => (a < b ? -1 : a > b ? 1 : 0))
                .map(x => ({ id: x, name: `${x}` }));

            const evaluationStatuses = [
                'translate_no_evaluations',
                'translate_all',
                'translate_with_evaluations',
            ].map((string, i) => ({
                id: i - 1,
                name: this.translateService.instant(string),
            }));

            this.tableListFilters = tableListFilters(
                this.shownFilters,
                levels,
                evaluationStatuses,
                this.showRiskType
            );
            this.applyFiltersFromUrl();
        });
    }

    private applyFiltersFromUrl(): void {
        const queryParams = this.activatedRoute.snapshot.queryParamMap;

        if (queryParams.has('impactIds')) {
            this.tableController.filter.data.impactIds =
                queryParams.getAll('impactIds');
        }
        if (queryParams.has('probabilityIds')) {
            this.tableController.filter.data.probabilityIds =
                queryParams.getAll('probabilityIds');
        }
        if (queryParams.has('levels')) {
            this.tableController.filter.data.levels = queryParams
                .getAll('levels')
                .map(x => parseInt(x));
        }
        if (queryParams.has('categoryIds')) {
            this.tableController.filter.data.categoryIds =
                queryParams.getAll('categoryIds');
        }
        if (queryParams.has('strategicGoalIds')) {
            this.tableController.filter.data.strategicGoalIds =
                queryParams.getAll('strategicGoalIds');
        }
        if (queryParams.has('managementStrategyIds')) {
            this.tableController.filter.data.managementStrategyIds =
                queryParams.getAll('managementStrategyIds');
        }
        if (queryParams.has('departmentIds')) {
            this.tableController.filter.data.departmentIds =
                queryParams.getAll('departmentIds');
        }
        this.tableController.filter$.next(true);
    }
}
