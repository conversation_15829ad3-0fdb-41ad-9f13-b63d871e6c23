<div class="flex h-full w-full gap-2">
    <div
        class="sidebar-container relative flex flex-col"
        [class]="isSidebarCollapsed ? 'w-16' : 'w-72'"
    >
        <button
            class="collapse-button absolute -right-3 top-3 z-10 flex h-6 w-6 items-center justify-center rounded-full bg-white text-primary shadow-md hover:bg-gray-100"
            (click)="toggleSidebar()"
            [appTooltip]="
                (isSidebarCollapsed ? 'translate_expand' : 'translate_collapse')
                    | translate
            "
        >
            <i
                class="fa-light"
                [ngClass]="
                    isSidebarCollapsed ? 'fa-angles-right' : 'fa-angles-left'
                "
            ></i>
        </button>
        <app-sidebar
            class="block overflow-y-scroll rounded bg-white"
            style="font-size: 0.85rem"
            [navItems]="navItems"
            [displayMode]="'dark'"
            [scrollToItemOnNavigation]="false"
            [enabledFilter]="true"
            [isSidebarCollapsed]="isSidebarCollapsed"
        ></app-sidebar>
    </div>

    <div class="flex-1 overflow-auto">
        <ng-container *ngIf="router.url.includes('system-setting')">
            <div
                class="m-0 flex h-full items-center justify-center overflow-hidden rounded bg-cover bg-center bg-no-repeat"
                [ngStyle]="{
                    backgroundImage: !loginBackgroundBlobUrl
                        ? 'none'
                        : 'url(\'' + loginBackgroundBlobUrl + '\')'
                }"
            >
                <app-image
                    [imageObservable]="appSettingFetcherService.appLogo()"
                    style="height: 150px; width: 300px"
                ></app-image>
            </div>
        </ng-container>
        <router-outlet></router-outlet>
    </div>
</div>
