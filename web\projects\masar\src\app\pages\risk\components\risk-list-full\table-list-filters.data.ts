import { ShownFilter } from '@masar/pages/risk/components/risk-list-full/shown-filter.type';
import { TableListFilter } from '@masar/features/dynamic-page/types';

export const tableListFilters: (
    shownFilters: ShownFilter[],
    levels: { id: number; name: string }[],
    evaluationStatuses: { id: number; name: string }[],
    showRiskType: boolean
) => TableListFilter[] = (
    shownFilters,
    levels,
    evaluationStatuses,
    showRiskType
) => {
    const filters: TableListFilter[] = [];

    if (shownFilters.includes('name')) {
        filters.push({
            type: 'text',
            label: 'translate_search_by_name',
            key: 'keyword',
        });
    }

    if (shownFilters.includes('category')) {
        filters.push({
            type: 'select',
            label: 'translate_category',
            key: 'categoryIds',
            miscApiOptions: { endPoint: 'risk-category' },
            isMultiple: true,
            bindLabel: 'name',
            bindValue: 'id',
        });
    }

    if (shownFilters.includes('management_strategy')) {
        filters.push({
            type: 'select',
            label: 'translate_management_strategy',
            key: 'managementStrategyIds',
            miscApiOptions: { endPoint: 'risk-management-strategy' },
            isMultiple: true,
            bindLabel: 'name',
            bindValue: 'id',
        });
    }

    if (shownFilters.includes('impact')) {
        filters.push({
            type: 'select',
            label: 'translate_impact',
            key: 'impactIds',
            miscApiOptions: { endPoint: 'risk-impact' },
            isMultiple: true,
            bindLabel: 'name',
            bindValue: 'id',
        });
    }

    if (shownFilters.includes('probability')) {
        filters.push({
            type: 'select',
            label: 'translate_probability',
            key: 'probabilityIds',
            miscApiOptions: { endPoint: 'risk-probability' },
            isMultiple: true,
            bindLabel: 'name',
            bindValue: 'id',
        });
    }

    if (shownFilters.includes('level')) {
        filters.push({
            type: 'select',
            label: 'translate_danger_level',
            key: 'levels',
            items: levels,
            isMultiple: true,
            bindLabel: 'name',
            bindValue: 'id',
        });
    }

    if (shownFilters.includes('department')) {
        filters.push({
            type: 'select',
            label: 'translate_departments',
            key: 'departmentIds',
            miscApiOptions: { endPoint: 'department' },
            isMultiple: true,
            bindLabel: 'name',
            bindValue: 'id',
        });
    }

    if (shownFilters.includes('strategic_goal')) {
        filters.push({
            type: 'select',
            label: 'translate_strategic_goals',
            key: 'strategicGoalIds',
            miscApiOptions: { endPoint: 'strategic-goal' },
            isMultiple: true,
            bindLabel: 'name',
            bindValue: 'id',
        });
    }

    if (shownFilters.includes('evaluation_status')) {
        filters.push({
            type: 'select',
            label: 'translate_with_evaluations',
            key: 'evaluationStatus',
            items: evaluationStatuses,
            isClearable: false,
            isSearchable: false,
            bindLabel: 'name',
            bindValue: 'id',
        });
    }

    if (shownFilters.includes('risk_type') && showRiskType) {
        filters.push({
            type: 'select',
            label: 'translate_type',
            key: 'types',
            miscApiOptions: { endPoint: 'risk-type' },
            isMultiple: true,
            bindLabel: 'name',
            bindValue: 'id',
        });
    }

    return filters;
};
