import {
    Component,
    EventEmitter,
    Input,
    OnChanges,
    OnDestroy,
    OnInit,
    Output,
    SimpleChanges,
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { takeUntil } from 'rxjs/operators';
import { Observable, Subject } from 'rxjs';
import { DetailPageData } from '@masar/features/dynamic-page/interfaces';

@Component({
    selector: 'app-detail-page',
    templateUrl: './detail-page.component.html',
})
export class DetailPageComponent implements OnInit, OnDestroy, OnChanges {
    @Input() public getItemCb!: <T extends { id: string }>(
        id: string
    ) => Observable<T>;

    @Input() public data!: DetailPageData<unknown>;

    @Output() public itemChange = new EventEmitter<unknown & { id: string }>();

    public item?: unknown & { id: string };

    public sections: DetailPageData<unknown>['sections'] = [];

    private id: string;

    private unsubscribeAll = new Subject();

    public constructor(private readonly activatedRoute: ActivatedRoute) {}

    public ngOnInit(): void {
        this.activatedRoute.params
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(params => {
                this.id = params['id'];
                if (!this.id) return;
                this.refresh();
            });
    }

    public ngOnChanges(changes: SimpleChanges): void {
        if (changes['data']) this.renderSections();
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    public refresh(): void {
        this.getItemCb(this.id).subscribe(item => {
            this.item = item;
            this.renderSections();
            this.itemChange.emit(item);
        });
    }

    private renderSections(): void {
        this.sections = [
            ...(this.data.sections || []),
            ...(this.data.sectionsFactory
                ? this.data.sectionsFactory?.(this.item)
                : []),
        ];
    }
}
