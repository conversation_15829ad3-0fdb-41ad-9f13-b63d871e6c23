import { Component, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import {
    AppSettingFetcherService,
    NotificationUnreadCountService,
} from '@masar/core/services';
import { settingNavItems } from '@masar/pages/system-settings/components/system-setting-sidebar/items.nav';
import { NavItem } from '@masar/shared/components/sidebar/types';
import { isOrigin } from '@masar/common/utils';
import { OrganizationOrigin } from '@masar/common/enums';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';

@Component({
    selector: 'app-system-setting',
    templateUrl: './system-setting.component.html',
    styles: [
        `
            :host {
                display: block;
                height: 100%;
            }

            .sidebar-container {
                transition: width 0.3s ease-in-out;
            }

            .collapse-button {
                transition: all 0.2s ease-in-out;
            }

            .collapse-button:hover {
                transform: scale(1.1);
            }
        `,
    ],
})
export class SystemSettingComponent implements OnDestroy {
    public navItems: NavItem[] = settingNavItems;
    public loginBackgroundBlobUrl: string;
    public isSidebarCollapsed = false;
    private unsubscribeAll = new Subject();

    public constructor(
        public router: Router,
        public appSettingFetcherService: AppSettingFetcherService,
        private unreadCountService: NotificationUnreadCountService
    ) {
        // Handle new user requests nav item
        const navItem = this.navItems.find(
            item => item.tag === 'newUserRequests'
        );
        if (navItem) {
            navItem.isHidden = !isOrigin([
                OrganizationOrigin.police,
                OrganizationOrigin.injaz,
                OrganizationOrigin.staging,
            ]);
        }

        // Set login background
        appSettingFetcherService.appLoginBackground().subscribe(blob => {
            this.loginBackgroundBlobUrl = URL.createObjectURL(blob);
        });

        // Handle unread count for new user requests
        this.unreadCountService.newUserRequestUnread$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(count => {
                if (navItem) {
                    navItem.badgeCount = count;
                }
            });

        // Handle app settings
        this.appSettingFetcherService.get$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(settings => {
                // Extract settings as enabled flags with defaults
                const settingsMap = {
                    ministryGoals:
                        settings.planSetting.optionalFields.find(
                            f => f.name === 'ministry_strategic_goals'
                        )?.isEnabled ?? false,
                    planCategory:
                        settings.planSetting.optionalFields.find(
                            f => f.name === 'plan_category'
                        )?.isEnabled ?? false,
                    planTaskCategory:
                        settings.planSetting.optionalFields.find(
                            f => f.name === 'plan_task_category'
                        )?.isEnabled ?? false,
                    resourcesClassification:
                        settings.planSetting.optionalSections.find(
                            s => s.name === 'other_resources'
                        )?.isEnabled ?? false,
                };

                // Define tag to settings mapping
                const tagToSettingMap = {
                    ministryStrategicGoal: 'ministryGoals',
                    planCategories: 'planCategory',
                    planTaskCategories: 'planTaskCategory',
                    planResourcesClassification: 'resourcesClassification',
                    planClassifiedResources: 'resourcesClassification',
                };

                // Update nav items based on settings
                this.navItems.forEach(section => {
                    if (section.type !== 'section') return;

                    if (
                        section.tag === 'strategicGoal' ||
                        section.tag === 'plans'
                    ) {
                        section.children?.forEach(item => {
                            const settingKey = tagToSettingMap[item.tag];
                            if (settingKey) {
                                item.isHidden = !settingsMap[settingKey];
                            }
                        });
                    }
                });
            });
    }

    public toggleSidebar(): void {
        this.isSidebarCollapsed = !this.isSidebarCollapsed;
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }
}
